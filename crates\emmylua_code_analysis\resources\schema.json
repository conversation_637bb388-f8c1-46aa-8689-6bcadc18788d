{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Emmyrc", "type": "object", "properties": {"$schema": {"type": ["string", "null"]}, "codeAction": {"default": {"insertSpace": false}, "allOf": [{"$ref": "#/definitions/EmmyrcCodeAction"}]}, "codeLens": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcCodeLen"}]}, "completion": {"default": {"autoRequire": true, "autoRequireFunction": "require", "autoRequireNamingConvention": "keep", "autoRequireSeparator": ".", "baseFunctionIncludesName": true, "callSnippet": false, "enable": true, "postfix": "@"}, "allOf": [{"$ref": "#/definitions/EmmyrcCompletion"}]}, "diagnostics": {"default": {"diagnosticInterval": 500, "disable": [], "enable": true, "enables": [], "globals": [], "globalsRegex": [], "severity": {}}, "allOf": [{"$ref": "#/definitions/EmmyrcDiagnostic"}]}, "documentColor": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcDocumentColor"}]}, "hint": {"default": {"enable": true, "indexHint": true, "localHint": true, "metaCallHint": true, "overrideHint": true, "paramHint": true}, "allOf": [{"$ref": "#/definitions/EmmyrcInlayHint"}]}, "hover": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcHover"}]}, "inlineValues": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcInlineValues"}]}, "references": {"default": {"enable": true, "fuzzySearch": true, "shortStringSearch": false}, "allOf": [{"$ref": "#/definitions/EmmyrcReference"}]}, "resource": {"default": {"paths": []}, "allOf": [{"$ref": "#/definitions/EmmyrcResource"}]}, "runtime": {"default": {"classDefaultCall": {"forceNonColon": false, "forceReturnSelf": false, "functionName": ""}, "extensions": [], "frameworkVersions": [], "requireLikeFunction": [], "requirePattern": [], "version": "LuaLatest"}, "allOf": [{"$ref": "#/definitions/EmmyrcRuntime"}]}, "semanticTokens": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcSemanticToken"}]}, "signature": {"default": {"detailSignatureHelper": true}, "allOf": [{"$ref": "#/definitions/EmmyrcSignature"}]}, "strict": {"default": {"arrayIndex": true, "docBaseConstMatchBaseType": true, "metaOverrideFileDefine": true, "requirePath": false, "typeCall": false}, "allOf": [{"$ref": "#/definitions/EmmyrcStrict"}]}, "workspace": {"default": {"enableReindex": false, "encoding": "utf-8", "ignoreDir": [], "ignoreGlobs": [], "library": [], "moduleMap": [], "preloadFileSize": 0, "reindexDuration": 5000, "workspaceRoots": []}, "allOf": [{"$ref": "#/definitions/EmmyrcWorkspace"}]}}, "definitions": {"ClassDefaultCall": {"type": "object", "properties": {"forceNonColon": {"description": "Mandatory non`:` definition. When `function_name` is not empty, it takes effect.", "default": true, "type": "boolean"}, "forceReturnSelf": {"description": "Force to return `self`.", "default": true, "type": "boolean"}, "functionName": {"description": "class default overload function. eg. \"__init\".", "default": "", "type": "string"}}}, "DiagnosticCode": {"oneOf": [{"type": "string", "enum": ["none"]}, {"description": "Syntax error", "type": "string", "enum": ["syntax-error"]}, {"description": "Doc syntax error", "type": "string", "enum": ["doc-syntax-error"]}, {"description": "Type not found", "type": "string", "enum": ["type-not-found"]}, {"description": "Missing return statement", "type": "string", "enum": ["missing-return"]}, {"description": "Param Type not match", "type": "string", "enum": ["param-type-not-match"]}, {"description": "Missing parameter", "type": "string", "enum": ["missing-parameter"]}, {"description": "Redundant parameter", "type": "string", "enum": ["redundant-parameter"]}, {"description": "Unreachable code", "type": "string", "enum": ["unreachable-code"]}, {"description": "Unused", "type": "string", "enum": ["unused"]}, {"description": "Undefined global", "type": "string", "enum": ["undefined-global"]}, {"description": "Deprecated", "type": "string", "enum": ["deprecated"]}, {"description": "Access invisible", "type": "string", "enum": ["access-invisible"]}, {"description": "Discard return value", "type": "string", "enum": ["discard-returns"]}, {"description": "Undefined field", "type": "string", "enum": ["undefined-field"]}, {"description": "Local const reassign", "type": "string", "enum": ["local-const-reassign"]}, {"description": "Iter variable reassign", "type": "string", "enum": ["iter-variable-reassign"]}, {"description": "Duplicate type", "type": "string", "enum": ["duplicate-type"]}, {"description": "Redefined local", "type": "string", "enum": ["redefined-local"]}, {"description": "Redefined label", "type": "string", "enum": ["redefined-label"]}, {"description": "Code style check", "type": "string", "enum": ["code-style-check"]}, {"description": "Need check nil", "type": "string", "enum": ["need-check-nil"]}, {"description": "Await in sync", "type": "string", "enum": ["await-in-sync"]}, {"description": "Doc tag usage error", "type": "string", "enum": ["annotation-usage-error"]}, {"description": "Return type mismatch", "type": "string", "enum": ["return-type-mismatch"]}, {"description": "Missing return value", "type": "string", "enum": ["missing-return-value"]}, {"description": "Redundant return value", "type": "string", "enum": ["redundant-return-value"]}, {"description": "Undefined Doc Param", "type": "string", "enum": ["undefined-doc-param"]}, {"description": "Duplicate doc field", "type": "string", "enum": ["duplicate-doc-field"]}, {"description": "Missing fields", "type": "string", "enum": ["missing-fields"]}, {"description": "Inject Field", "type": "string", "enum": ["inject-field"]}, {"description": "Circle Doc Class", "type": "string", "enum": ["circle-doc-class"]}, {"description": "Incomplete signature doc", "type": "string", "enum": ["incomplete-signature-doc"]}, {"description": "Missing global doc", "type": "string", "enum": ["missing-global-doc"]}, {"description": "Assign type mismatch", "type": "string", "enum": ["assign-type-mismatch"]}, {"description": "Duplicate require", "type": "string", "enum": ["duplicate-require"]}, {"description": "non-literal-expressions-in-assert", "type": "string", "enum": ["non-literal-expressions-in-assert"]}, {"description": "Unbalanced assignments", "type": "string", "enum": ["unbalanced-assignments"]}, {"description": "unnecessary-assert", "type": "string", "enum": ["unnecessary-assert"]}, {"description": "unnecessary-if", "type": "string", "enum": ["unnecessary-if"]}, {"description": "duplicate-set-field", "type": "string", "enum": ["duplicate-set-field"]}, {"description": "duplicate-index", "type": "string", "enum": ["duplicate-index"]}, {"description": "generic-constraint-mismatch", "type": "string", "enum": ["generic-constraint-mismatch"]}, {"description": "cast-type-mismatch", "type": "string", "enum": ["cast-type-mismatch"]}]}, "DiagnosticSeveritySetting": {"oneOf": [{"description": "Represents an error diagnostic severity.", "type": "string", "enum": ["error"]}, {"description": "Represents a warning diagnostic severity.", "type": "string", "enum": ["warning"]}, {"description": "Represents an information diagnostic severity.", "type": "string", "enum": ["information"]}, {"description": "Represents a hint diagnostic severity.", "type": "string", "enum": ["hint"]}]}, "EmmyrcCodeAction": {"type": "object", "properties": {"insertSpace": {"description": "Whether to insert space after '---'", "default": false, "type": "boolean"}}}, "EmmyrcCodeLen": {"type": "object", "properties": {"enable": {"description": "Whether to enable code lens.", "default": true, "type": "boolean"}}}, "EmmyrcCompletion": {"description": "Configuration for EmmyLua code completion.", "type": "object", "properties": {"autoRequire": {"description": "Whether to automatically require modules.", "default": true, "type": "boolean"}, "autoRequireFunction": {"description": "The function used for auto-requiring modules.", "default": "require", "type": "string"}, "autoRequireNamingConvention": {"description": "The naming convention for auto-required filenames.", "default": "keep", "allOf": [{"$ref": "#/definitions/EmmyrcFilenameConvention"}]}, "autoRequireSeparator": {"description": "A separator used in auto-require paths.", "default": ".", "type": "string"}, "baseFunctionIncludesName": {"description": "Whether to include the name in the base function completion. effect: `function () end` -> `function name() end`.", "default": true, "type": "boolean"}, "callSnippet": {"description": "Whether to use call snippets in completions.", "default": false, "type": "boolean"}, "enable": {"description": "Whether to enable code completion.", "default": true, "type": "boolean"}, "postfix": {"description": "The postfix trigger used in completions.", "default": "@", "type": "string"}}}, "EmmyrcDiagnostic": {"description": "Represents the diagnostic configuration for Emmyrc.", "type": "object", "properties": {"diagnosticInterval": {"description": "The interval in milliseconds to perform diagnostics.", "type": ["integer", "null"], "format": "uint64", "minimum": 0.0}, "disable": {"description": "A list of diagnostic codes that are disabled.", "default": [], "type": "array", "items": {"$ref": "#/definitions/DiagnosticCode"}}, "enable": {"description": "A flag indicating whether diagnostics are enabled.", "default": true, "type": "boolean"}, "enables": {"description": "A list of diagnostic codes that are enabled.", "default": [], "type": "array", "items": {"$ref": "#/definitions/DiagnosticCode"}}, "globals": {"description": "A list of global variables.", "default": [], "type": "array", "items": {"type": "string"}}, "globalsRegex": {"description": "A list of regular expressions for global variables.", "default": [], "type": "array", "items": {"type": "string"}}, "severity": {"description": "A map of diagnostic codes to their severity settings.", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/definitions/DiagnosticSeveritySetting"}}}}, "EmmyrcDocumentColor": {"type": "object", "properties": {"enable": {"description": "Whether to enable document color.", "default": true, "type": "boolean"}}}, "EmmyrcFilenameConvention": {"oneOf": [{"description": "Keep the original filename.", "type": "string", "enum": ["keep"]}, {"description": "Convert the filename to snake_case.", "type": "string", "enum": ["snake-case"]}, {"description": "Convert the filename to PascalCase.", "type": "string", "enum": ["pascal-case"]}, {"description": "Convert the filename to camelCase.", "type": "string", "enum": ["camel-case"]}]}, "EmmyrcHover": {"type": "object", "properties": {"enable": {"description": "Whether to enable hover.", "default": true, "type": "boolean"}}}, "EmmyrcInlayHint": {"type": "object", "properties": {"enable": {"description": "Whether to enable inlay hints.", "default": true, "type": "boolean"}, "indexHint": {"description": "Whether to enable index hints.", "default": true, "type": "boolean"}, "localHint": {"description": "Whether to enable local hints. Whether to enable override hints.", "default": true, "type": "boolean"}, "metaCallHint": {"description": "Whether to enable meta __call operator hints.", "default": true, "type": "boolean"}, "overrideHint": {"description": "Whether to enable override hints.", "default": true, "type": "boolean"}, "paramHint": {"description": "Whether to enable parameter hints.", "default": true, "type": "boolean"}}}, "EmmyrcInlineValues": {"type": "object", "properties": {"enable": {"description": "Whether to enable inline values.", "default": true, "type": "boolean"}}}, "EmmyrcLuaVersion": {"oneOf": [{"description": "Lua 5.1", "type": "string", "enum": ["Lua5.1"]}, {"description": "LuaJIT", "type": "string", "enum": ["LuaJIT"]}, {"description": "Lua 5.2", "type": "string", "enum": ["Lua5.2"]}, {"description": "Lua 5.3", "type": "string", "enum": ["Lua5.3"]}, {"description": "Lua 5.4", "type": "string", "enum": ["Lua5.4"]}, {"description": "Lua 5.5", "type": "string", "enum": ["Lua5.5"]}, {"description": "<PERSON><PERSON>", "type": "string", "enum": ["LuaLatest"]}]}, "EmmyrcReference": {"type": "object", "properties": {"enable": {"description": "Whether to enable reference search.", "default": true, "type": "boolean"}, "fuzzySearch": {"description": "Determines whether to enable fuzzy searching for fields where references cannot be found.", "default": true, "type": "boolean"}, "shortStringSearch": {"description": "<PERSON><PERSON> Short string for search", "default": false, "type": "boolean"}}}, "EmmyrcResource": {"type": "object", "properties": {"paths": {"default": [], "type": "array", "items": {"type": "string"}}}}, "EmmyrcRuntime": {"type": "object", "properties": {"classDefaultCall": {"description": "class default overload function.", "default": {"forceNonColon": false, "forceReturnSelf": false, "functionName": ""}, "allOf": [{"$ref": "#/definitions/ClassDefaultCall"}]}, "extensions": {"description": "file Extensions. eg: .lua, .lua.txt", "default": [], "type": "array", "items": {"type": "string"}}, "frameworkVersions": {"description": "Framework versions.", "default": [], "type": "array", "items": {"type": "string"}}, "requireLikeFunction": {"description": "Functions that like require.", "default": [], "type": "array", "items": {"type": "string"}}, "requirePattern": {"description": "Require pattern. eg. \"?.lua\", \"?/init.lua\"", "default": [], "type": "array", "items": {"type": "string"}}, "version": {"description": "Lua version.", "default": "LuaLatest", "allOf": [{"$ref": "#/definitions/EmmyrcLuaVersion"}]}}}, "EmmyrcSemanticToken": {"type": "object", "properties": {"enable": {"description": "Whether to enable semantic token.", "default": true, "type": "boolean"}}}, "EmmyrcSignature": {"type": "object", "properties": {"detailSignatureHelper": {"description": "Whether to enable signature help.", "default": true, "type": "boolean"}}}, "EmmyrcStrict": {"type": "object", "properties": {"arrayIndex": {"description": "Whether to enable strict mode array indexing.", "default": true, "type": "boolean"}, "docBaseConstMatchBaseType": {"description": "Base constant types defined in doc can match base types, allowing int to match `---@alias id 1|2|3`, same for string.", "default": false, "type": "boolean"}, "metaOverrideFileDefine": {"description": "meta define overrides file define", "default": true, "type": "boolean"}, "requirePath": {"description": "Whether to enable strict mode require path.", "default": false, "type": "boolean"}, "typeCall": {"default": false, "type": "boolean"}}}, "EmmyrcWorkspace": {"type": "object", "properties": {"enableReindex": {"description": "Enable reindex.", "default": false, "type": "boolean"}, "encoding": {"description": "Encoding. eg: \"utf-8\"", "default": "utf-8", "type": "string"}, "ignoreDir": {"description": "Ignore directories.", "default": [], "type": "array", "items": {"type": "string"}}, "ignoreGlobs": {"description": "Ignore globs. eg: [\"**/*.lua\"]", "default": [], "type": "array", "items": {"type": "string"}}, "library": {"description": "Library paths. eg: \"/usr/local/share/lua/5.1\"", "default": [], "type": "array", "items": {"type": "string"}}, "moduleMap": {"description": "Module map. key is regex, value is new module regex eg: { \"^(.*)$\": \"module_$1\" \"^lib(.*)$\": \"script$1\" }", "default": [], "type": "array", "items": {"$ref": "#/definitions/EmmyrcWorkspaceModuleMap"}}, "preloadFileSize": {"default": 0, "type": "integer", "format": "int32"}, "reindexDuration": {"description": "when save a file, ls will reindex the workspace after reindex_duration milliseconds.", "default": 5000, "type": "integer", "format": "uint64", "minimum": 0.0}, "workspaceRoots": {"description": "Workspace roots. eg: [\"src\", \"test\"]", "default": [], "type": "array", "items": {"type": "string"}}}}, "EmmyrcWorkspaceModuleMap": {"type": "object", "required": ["pattern", "replace"], "properties": {"pattern": {"type": "string"}, "replace": {"type": "string"}}}}}